/* eslint-disable no-unused-vars */
import { Track } from "livekit-client";
import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  MediaDeviceMenu,
  DisconnectButton,
  useLocalParticipantPermissions,
  usePersistentUserChoices,
  useMaybeLayoutContext,
} from "@livekit/components-react";
import {
  supportsScreenSharing,
  isMobileBrowser,
} from "@livekit/components-core";
import { Tooltip, Badge, Popover } from "antd";
import { TrackToggle } from "../components/TrackToggle";

import { DrawerState } from "../utils/constants";
import { onDeviceError } from "../utils/helper";
import { useMediaQuery } from "../hooks/useMediaQuerry";
import { mergeProps } from "../utils/mergeProps";
import { StartMediaButton } from "../components/StartMediaButton";
import { SettingsControlButton } from "../components/settings/SettingsControlButton";
import { InfoPopover } from "../components/InfoPopover";
import { ParticipantControlButton } from "../components/participants/ParticipantControlButton";
import { RaiseHandControlButton } from "../components/raisehand/RaiseHandControlButton";
import { ReactionsControlButton } from "../components/reactions/ReactionsControlButton";
import { DisconnectButtonMenu } from "../components/Disconnect/DisconnectButtonMenu";
import { ScreenShareMenuButton } from "../components/ScreenShare/ScreenShareMenuButton";
import { Timer } from "../components/Timer";
import RealTimeAudioWave from "../components/RealTimeAudioWave";
import { ReactComponent as LeaveIcon } from "../assets/icons/LeaveIcon.svg";
import { ReactComponent as RecordingIconOn } from "../assets/icons/RecordingOnState.svg";
import ChatControlButton from "../components/chats/ChatControlButton";
import AudioDeviceDropdown from "../components/AudioDeviceDropdown";

// Context import
import { useVideoConferencesContext } from "../context/VideoConferencesContext";
import { VideoConferenceService } from "../services/VideoConferenceServices";
import { usePictureInPicture } from "./PictureInPicture";
import { useSaasHelpers } from "../SaaS/helpers/helpers";

export function ControlBar({
  variation,
  controls,
  connnected,
  showRaiseHand,
  setShowRaiseHand,
  showEmojiReaction,
  setShowEmojiReaction,
  showRecording,
  saveUserChoices = true,
  isForceMuteAll,
  isForceVideoOffAll,
  coHostToken,
  isBreakoutRoom,
  localParticipant,
  remoteParticipants,
  meetingFeatures,
  privateChatUnreadMessagesCount,
  publicChatUnreadMessagesCount,
  showlivecaptionsicon,
  isWebinarMode,
  isElectronApp,
  screenShareSources,
  isExitWhiteboardModalOpen,
  setIsExitWhiteboardModalOpen,
  whiteboardSceneData,
  setWhiteboardSceneData,
  whiteBoardId,
  setWhiteboardId,
  isAnnotationEnabled,
  setIsAnnotationEnabled,
  setScreenShareDisplayId,
  isScreenShareEnabled,
  setIsScreenShareEnabled,
  screenShareMode,
  setScreenShareMode,
  onScreenShareChange,
  setToastNotification,
  setToastStatus,
  setShowToast,
  deviceIdAudio,
  setDeviceIdAudio,
  deviceIdVideo,
  setDeviceIdVideo,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  isRecordingLoading,
  setIsRecordingLoading,
  setParticipantConsent,
  setShowRecordingConsentDrawer,
  showRecordingConsentDrawer,
  brightness,
  onBrightnessChange,
  // Volume props (following brightness pattern, no RPC needed)
  outputVolume,
  onOutputVolumeChange,
  // Auto video off props (following volume pattern, no RPC needed)
  autoVideoOff,
  onAutoVideoOffChange,
  // Auto audio off props (following volume pattern, no RPC needed)
  autoAudioOff,
  onAutoAudioOffChange,
  speakerDeviceId,
  setSpeakerDeviceId,
  connected,
  room,
  ...props
}) {
  // Context
  const { setOpenDrawer } = useVideoConferencesContext();
  const [sipData, setSipData] = useState(null);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [showDisconnectPopover, setShowDisconnectPopover] = useState(false);
  const [hasLoggedHold, setHasLoggedHold] = useState(false);
  const remote = Array.from(remoteParticipants.values());
  const remoteParticipantsArray = [localParticipant, ...remote];
  const [isPIPEnabled, setIsPIPEnabled] = useState(false);
  const layoutContext = useMaybeLayoutContext();
  const { dispatch, state } = layoutContext.widget;
  const { saasHostToken } = useSaasHelpers();

  const { togglePipMode, pipPortal, isSupported: isPipSupported } = usePictureInPicture({
    setIsPIPEnabled,
    setToastNotification,
    setToastStatus,
    setShowToast,
    localParticipant,
    room,
    isHost: props.isHost,
    isCoHost: props.isCoHost,
  });

  useEffect(() => {
    if (layoutContext?.widget.state?.showChat !== undefined) {
      setIsChatOpen(layoutContext?.widget.state?.showChat);
    }
    if (layoutContext?.widget.state?.showChat === true) {
      setOpenDrawer((prev) => prev === DrawerState.CHAT ? DrawerState.NONE : DrawerState.CHAT);
    }
  }, [layoutContext?.widget.state?.showChat]);

  // Handle PiP state changes
  useEffect(() => {
    if (isPIPEnabled === null) return;
    togglePipMode(isPIPEnabled);
  }, [isPIPEnabled, togglePipMode]);

  const isTooLittleSpace = useMediaQuery(
    `(max-width: ${isChatOpen ? 1000 : 760}px)`
  );

  const defaultVariation = "minimal";
  variation = variation || defaultVariation;

  const visibleControls = { leave: true, ...controls };

  const localPermissions = useLocalParticipantPermissions();

  if (!localPermissions) {
    visibleControls.camera = false;
    visibleControls.chat = false;
    visibleControls.microphone = false;
    visibleControls.screenShare = false;
  } else {
    visibleControls.camera =
      visibleControls.camera ?? localPermissions.canPublish;
    visibleControls.microphone =
      visibleControls.microphone ?? localPermissions.canPublish;
    visibleControls.screenShare =
      visibleControls.screenShare ?? localPermissions.canPublish;
    visibleControls.chat =
      visibleControls.chat ??
      (localPermissions.canPublishData && controls?.chat);
  }

  const showIcon = useMemo(
    () => variation === "minimal" || variation === "verbose",
    [variation]
  );
  const showText = useMemo(
    () => variation === "textOnly" || variation === "verbose",
    [variation]
  );

  const browserSupportsScreenSharing = supportsScreenSharing();

  const htmlProps = mergeProps({ className: "lk-control-bar" }, props);

  const {
    saveAudioInputEnabled,
    saveVideoInputEnabled,
    saveAudioInputDeviceId,
    saveVideoInputDeviceId,
  } = usePersistentUserChoices({ preventSave: !saveUserChoices });

  const microphoneOnChange = useCallback(
    (enabled, isUserInitiated) =>
      isUserInitiated ? saveAudioInputEnabled(enabled) : null,
    [saveAudioInputEnabled]
  );

  const cameraOnChange = useCallback(
    (enabled, isUserInitiated) =>
      isUserInitiated ? saveVideoInputEnabled(enabled) : null,
    [saveVideoInputEnabled]
  );

  const [controlAudioId, setControlAudioId] = useState("");
  const [controlVideoId, setControlVideoId] = useState("");

  useEffect(() => {
    console.log('🎤 ControlBar: deviceIdAudio value:', deviceIdAudio);
    console.log('📹 ControlBar: deviceIdVideo value:', deviceIdVideo);
    setControlAudioId(deviceIdAudio);
    setControlVideoId(deviceIdVideo);
  }, [deviceIdAudio, deviceIdVideo]);

    useEffect(() => {
      if (!connected) return;
      const fetchSipConfig = async () => {
        if (connected) {
          try {
            const sipResponse = await VideoConferenceService.sipConnection(
                props.id,
                props.room?.localParticipant?.participantInfo,
                saasHostToken
            );
            if (sipResponse?.success === 1) {
              setSipData(sipResponse?.data);
            }
          } catch (error) {
            setToastNotification(error.message);
            setToastStatus("error");
            setShowToast(true);
            // console.log(error);
          }
        }
      };
      fetchSipConfig();
    }, [connected]);

  // Add key press event handler
  useEffect(() => {
    const handleKeyDown = async (event) => {
      if (event.code === 'Space' && !hasLoggedHold) {
        console.log('Space pressed - Starting push-to-talk');
        
        // Enable microphone
        localParticipant.setMicrophoneEnabled(true);
        setHasLoggedHold(true);
        console.log('Microphone enabled');

        // Find agent participant
        const agentParticipant = Array.from(remoteParticipants.values()).find(
          participant => participant.identity.startsWith('agent-')
        );

        if (agentParticipant) {
          console.log('Found agent:', agentParticipant.identity);
          try {
            // Send start_turn RPC to agent
            await localParticipant.performRpc({
              destinationIdentity: agentParticipant.identity,
              method: "start_turn",
              payload: "",
            });
            console.log('✅ Successfully sent start_turn to agent:', agentParticipant.identity);
          } catch (error) {
            console.error('❌ Failed to send start_turn to agent:', error);
          }
        } else {
          console.log('No agent found in the room');
        }
      }
    };


    const handleKeyUp = async (event) => {
      if (event.code === 'Space') {
        // console.log('Space released - Ending push-to-talk');
        
        // Disable microphone
        localParticipant.setMicrophoneEnabled(false);
        setHasLoggedHold(false);
        // console.log('Microphone disabled');

        // Find agent participant
        const agentParticipant = Array.from(remoteParticipants.values()).find(
          participant => participant.identity.startsWith('agent-')
        );

        if (agentParticipant) {
          // console.log('Found agent:', agentParticipant.identity);
          try {
            // Send end_turn RPC to agent
            await localParticipant.performRpc({
              destinationIdentity: agentParticipant.identity,
              method: "end_turn",
              payload: "",
            });
            // console.log('✅ Successfully sent end_turn to agent:', agentParticipant.identity);
          } catch (error) {
            console.error('❌ Failed to send end_turn to agent:', error);
          }
        } else {
          // console.log('No agent found in the room');
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    // Cleanup event listeners
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [hasLoggedHold, remoteParticipants, localParticipant]);

  return (
    <div {...htmlProps} className="lk-control-bar control-bar-container">
      <div
        style={{
          display: "flex",
          alignItems: "center",
        }}
      >
        <Tooltip placement="topLeft" title="Recording in progress">
          {!isMobileBrowser() && showRecording && (
            <RecordingIconOn
              style={{
                position: "absolute",
                left: "20px",
              }}
              className="recording-icon"
            />
          )}
        </Tooltip>

        <div
          style={{
            display: "flex",
            alignItems: "center",
          }}
        >
          {!isMobileBrowser() ? (
            <>
              <div
                className={
                  showRecording
                    ? "controlbar-timer-recording-on"
                    : "controlbar-timer"
                }
                style={{ cursor: "pointer" }}
              >
                <Timer meetingDetails={props.meetingDetails} />
              </div>
              {(meetingFeatures?.configurations ? meetingFeatures?.configurations?.show_meeting_info === 1 : visibleControls.info) && (
                <InfoPopover
                  id={props.id}
                  meetingDetails={props.meetingDetails}
                  meetingFeatures={meetingFeatures}
                  connnected={connnected}
                  sipData={sipData}
                  setToastNotification={setToastNotification}
                  setToastStatus={setToastStatus}
                  setShowToast={setShowToast}
                />
              )}
            </>
          ) : null}
        </div>
      </div>

      {visibleControls.microphone && (
        <div
          className={`lk-button-group audio-control-group ${
            isForceMuteAll && (!props.isCoHost || !props.isHost)
              ? "disabled"
              : ""
          }`}
        >
          <Popover
            content={"Audio Level"}
            // trigger={"click"}
            showArrow={false}
            overlayClassName="control-bar-hover-label"
          >
            <div className="lk-button-group-menu audio-control-group__device-left">
              <RealTimeAudioWave/>
            </div>
          </Popover>
          <Popover
            content={"Microphone"}
            // trigger={"click"}
            showArrow={false}
            overlayClassName="control-bar-hover-label"
          >
            <TrackToggle
              source={Track.Source.Microphone}
              showIcon={showIcon}
              onChange={microphoneOnChange}
              className="control-bar-button control-bar-button-icon audio-control-group__microphone-toggle"
              onDeviceError={onDeviceError}
            >
              {showText && "Microphone"}
            </TrackToggle>
          </Popover>
            <div className="lk-button-group-menu audio-control-group__device-right">
              <AudioDeviceDropdown
                kind="audioinput"
                onActiveDeviceChange={(deviceId) => {
                  console.log('🎤 ControlBar: AudioDeviceDropdown changed to:', deviceId);
                  saveAudioInputDeviceId(deviceId ?? "");
                  setDeviceIdAudio(deviceId ?? "");
                }}
                initialSelection={controlAudioId}

              />
            </div>
        </div>
      )}
      {visibleControls.camera && (
        <Popover
          content={"Camera"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <div
            className={`lk-button-group ${
              isForceVideoOffAll && (!props.isCoHost || !props.isHost)
                ? "disabled"
                : ""
            }`}
          >
            <TrackToggle
              source={Track.Source.Camera}
              showIcon={showIcon}
              onChange={cameraOnChange}
              className="control-bar-button-camera control-bar-button control-bar-button-icon"
              onDeviceError={onDeviceError}
            >
              {showText && "Camera"}
            </TrackToggle>
            {!isMobileBrowser() && (
              <div className="lk-button-group-menu">
                <MediaDeviceMenu
                  kind="videoinput"
                  onActiveDeviceChange={(_kind, deviceId) =>
                    saveVideoInputDeviceId(deviceId ?? "")
                  }
                />
              </div>
            )}
          </div>
        </Popover>
      )}
      {/* Chat button */}
      {meetingFeatures?.conference_chat === 1 && visibleControls.chat && (
        <Popover
          content={"Chats"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <Badge
            count={
              privateChatUnreadMessagesCount + publicChatUnreadMessagesCount
            }
            overflowCount={9}
            color="blue"
          >
            <ChatControlButton />
          </Badge>
        </Popover>
      )}

      {/* Reaction Control Button */}
      {!isMobileBrowser() && (meetingFeatures?.configurations?.enable_reaction === 1 || !meetingFeatures?.configurations) ? (
        <Popover
          content={"Reactions"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <div className="control-bar-button-reactions">
            <ReactionsControlButton
              showEmojiReaction={showEmojiReaction}
              setShowEmojiReaction={setShowEmojiReaction}
            />
          </div>
        </Popover>
      ) : null}
      {!isMobileBrowser() &&
      isWebinarMode &&
      (props.isHost || props.isCoHost) ? (
        <Popover
          content={"Share Screen"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <>
            {meetingFeatures?.screen_sharing === 1 &&
              visibleControls.screenShare &&
              browserSupportsScreenSharing &&
              (isScreenShareEnabled ? (
                <TrackToggle
                  source={Track.Source.ScreenShare}
                  captureOptions={
                    screenShareMode === "text"
                      ? {
                          audio: true,
                          contentHint: "detail",
                          resolution: {
                            width: props.maxWidth,
                            height: props.maxHeight,
                            frameRate: 5,
                          },
                        }
                      : {
                          audio: true,
                          contentHint: "detail",
                          resolution: {
                            width: props.maxWidth,
                            height: props.maxHeight,
                            frameRate: 30,
                          },
                        }
                  }
                  showIcon={showIcon}
                  onChange={onScreenShareChange}
                  onClick={() => setIsPIPEnabled(false)}
                  onDeviceError={onDeviceError}
                  className="control-bar-button control-bar-button-icon"
                />
              ) : (
                <ScreenShareMenuButton
                  onScreenShareChange={onScreenShareChange}
                  maxWidth={props.maxWidth}
                  maxHeight={props.maxHeight}
                  roomData={props.room}
                  setScreenShareMode={setScreenShareMode}
                  meetingFeatures={meetingFeatures}
                  setIsPIPEnabled={setIsPIPEnabled}
                  screenShareSources={screenShareSources}
                  isElectronApp={isElectronApp}
                  room={props.room}
                  setScreenShareDisplayId={setScreenShareDisplayId}
                  setToastNotification={setToastNotification}
                  setToastStatus={setToastStatus}
                  setShowToast={setShowToast}
                />
              ))}
          </>
        </Popover>
      ) : !isMobileBrowser() && !isWebinarMode ? (
        <Popover
          content={"Share Screen"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <>
            {meetingFeatures?.screen_sharing === 1 &&
              visibleControls.screenShare &&
              browserSupportsScreenSharing &&
              (isScreenShareEnabled ? (
                <TrackToggle
                  source={Track.Source.ScreenShare}
                  captureOptions={
                    screenShareMode === "text"
                      ? {
                          audio: true,
                          contentHint: "detail",
                          resolution: {
                            width: props.maxWidth,
                            height: props.maxHeight,
                            frameRate: 5,
                          },
                        }
                      : {
                          audio: true,
                          contentHint: "detail",
                          resolution: {
                            width: props.maxWidth,
                            height: props.maxHeight,
                            frameRate: 30,
                          },
                        }
                  }
                  showIcon={showIcon}
                  onChange={onScreenShareChange}
                  onClick={() => setIsPIPEnabled(false)}
                  onDeviceError={onDeviceError}
                  className="control-bar-button control-bar-button-icon"
                />
              ) : (
                <ScreenShareMenuButton
                  onScreenShareChange={onScreenShareChange}
                  maxWidth={props.maxWidth}
                  maxHeight={props.maxHeight}
                  roomData={props.room}
                  setScreenShareMode={setScreenShareMode}
                  meetingFeatures={meetingFeatures}
                  setIsPIPEnabled={setIsPIPEnabled}
                  screenShareSources={screenShareSources}
                  isElectronApp={isElectronApp}
                  room={props.room}
                  setScreenShareDisplayId={setScreenShareDisplayId}
                  setToastNotification={setToastNotification}
                  setToastStatus={setToastStatus}
                  setShowToast={setShowToast}
                />
              ))}
          </>
        </Popover>
      ) : null}
      {/* {isScreenShareEnabled && isElectronApp && (
        <AnnotationControlButton
          isElectronApp={isElectronApp}
          screenShareFocus={isScreenShareEnabled}
          isAnnotationEnabled={isAnnotationEnabled}
          setIsAnnotationEnabled={setIsAnnotationEnabled}
        />
      )} */}
      {meetingFeatures?.raise_hand === 1 && !isMobileBrowser() && (
        <Popover
          content={"Raise Hand"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <>
            <RaiseHandControlButton
              showRaiseHand={showRaiseHand}
              setShowRaiseHand={setShowRaiseHand}
            />
          </>
        </Popover>
      )}
      {!isMobileBrowser() &&
      isWebinarMode &&
      (props.isHost || props.isCoHost) ? (
        <Popover
          content={"Participants"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <Badge
            count={remoteParticipantsArray.length}
            color="transparent"
            style={{ boxShadow: "none" }}
          >
            <ParticipantControlButton />
          </Badge>
        </Popover>
      ) : !isMobileBrowser() && !isWebinarMode ? (
        <Popover
          content={"Participants"}
          showArrow={false}
          overlayClassName="control-bar-hover-label"
        >
          <>
            <Badge
              count={remoteParticipantsArray.length}
              color="transparent"
              style={{ boxShadow: "none" }}
            >
              <ParticipantControlButton />
            </Badge>
          </>
        </Popover>
      ) : null}
      {/* {!isMobileBrowser() ? (
        <> */}
      {visibleControls.settings && (
        <div className="control-bar-more-options">
          <span>More Options</span>
          <SettingsControlButton
            id={props.id}
            isHost={props.isHost}
            meetingDetails={props.meetingDetails}
            setShowRecording={props.setShowRecording}
            isCoHost={props.isCoHost}
            showRaiseHand={showRaiseHand}
            setShowRaiseHand={setShowRaiseHand}
            coHostToken={coHostToken}
            showRecording={showRecording}
            room={room}
            isBreakoutRoom={isBreakoutRoom}
            meetingFeatures={meetingFeatures}
            showlivecaptionsicon={showlivecaptionsicon}
            isPIPEnabled={isPIPEnabled}
            setIsPIPEnabled={setIsPIPEnabled}
            isWhiteboardOpen={props.isWhiteboardOpen}
            setIsWhiteboardOpen={props.setIsWhiteboardOpen}
            isExitWhiteboardModalOpen={isExitWhiteboardModalOpen}
            setIsExitWhiteboardModalOpen={setIsExitWhiteboardModalOpen}
            whiteboardSceneData={whiteboardSceneData}
            setWhiteboardSceneData={setWhiteboardSceneData}
            whiteBoardId={whiteBoardId}
            setWhiteboardId={setWhiteboardId}
            isSelfVideoMirrored={isSelfVideoMirrored}
            setIsSelfVideoMirrored={setIsSelfVideoMirrored}
            setToastNotification={setToastNotification}
            setToastStatus={setToastStatus}
            setShowToast={setShowToast}
            isRecordingLoading={isRecordingLoading}
            setIsRecordingLoading={setIsRecordingLoading}
            setParticipantConsent={setParticipantConsent}
            remoteParticipants={remoteParticipants}
            brightness={brightness}
            onBrightnessChange={onBrightnessChange}
            outputVolume={outputVolume}
            onOutputVolumeChange={onOutputVolumeChange}
            autoVideoOff={autoVideoOff}
            onAutoVideoOffChange={onAutoVideoOffChange}
            autoAudioOff={autoAudioOff}
            onAutoAudioOffChange={onAutoAudioOffChange}
            screenShareTracks={props.screenShareTracks}
            focusTrack={props.focusTrack}
            speakerDeviceId={speakerDeviceId}
            setSpeakerDeviceId={setSpeakerDeviceId}
          />
        </div>
      )}

      {visibleControls.leave &&
        (props.isHost || props.isCoHost ? (
          <div className="control-bar-more-options">
            <span>Disconnect</span>
            <DisconnectButtonMenu
              id={props.id}
              setShowPopover={setShowDisconnectPopover}
              showPopover={showDisconnectPopover}
              coHostToken={coHostToken}
              isElectronApp={isElectronApp}
              setToastNotification={setToastNotification}
              setToastStatus={setToastStatus}
              setShowToast={setShowToast}
              // isAnnotationEnabled={isAnnotationEnabled}
            />
          </div>
        ) : (
          <div className="control-bar-more-options">
            <span>Disconnect</span>
            <DisconnectButton
              className="control-bar-button control-bar-button-icon"
              style={{ backgroundColor: "rgba(255, 59, 48, 1)" }}
              onClick={() => {
                if (isElectronApp) {
                  window?.electronAPI?.ipcRenderer?.send("stop-annotation");
                }
              }}
            >
              {showIcon && <LeaveIcon />}
              {showText && "Leave"}
            </DisconnectButton>
          </div>
        ))}
      <StartMediaButton />
      {pipPortal}
    </div>
  );
}

