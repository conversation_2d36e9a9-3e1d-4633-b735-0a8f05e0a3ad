.audio-device-dropdown {
  position: relative;
  display: inline-block;

  &-button {
    background: transparent;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: background-color 0.2s;
    color: inherit;

 
  }

  &-menu {
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    max-height: 200px;
    overflow-y: auto;
    padding: 4px;

    .device-option {
      width: 100%;
      text-align: left;
      padding: 8px 12px;
      border: none;
      background: transparent;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 2px;
      color: #333;
      transition: all 0.2s ease;
      font-size: 14px;
      display: block;

      &:hover {
        background-color: #f5f5f5;
      }

      &.selected {
        background-color: #1890ff !important;
        color: white !important;
        font-weight: 500;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
