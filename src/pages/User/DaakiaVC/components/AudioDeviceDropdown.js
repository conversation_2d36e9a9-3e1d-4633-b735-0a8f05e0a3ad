import React, { useState, useEffect } from "react";
import { Dropdown } from "antd";
import { useMediaDeviceSelect } from "@livekit/components-react";
import "./AudioDeviceDropdown.scss";

function AudioDeviceDropdown({
  kind = "audioinput",
  onActiveDeviceChange,
  initialSelection,
  className = ""
}) {
  const [selectedDevice, setSelectedDevice] = useState(initialSelection);
  const { devices, activeDeviceId, setActiveMediaDevice } = useMediaDeviceSelect({ kind });

  useEffect(() => {
    setSelectedDevice(activeDeviceId || initialSelection);
  }, [initialSelection, activeDeviceId]);

  const handleDeviceChange = async (deviceId) => {
    try {
      await setActiveMediaDevice(deviceId);
      setSelectedDevice(deviceId);

      if (onActiveDeviceChange) {
        onActiveDeviceChange(deviceId);
      }
    } catch (error) {
      console.error('Failed to switch device:', error);
    }
  };

  const dropdownContent = (
    <div className="audio-device-dropdown-menu">
      {devices.map((device) => (
        <button
          key={device.deviceId}
          className={`device-option ${selectedDevice === device.deviceId ? 'selected' : ''}`}
          onClick={() => handleDeviceChange(device.deviceId)}
        >
          {device.label || `${kind} ${device.deviceId.slice(0, 8)}...`}
        </button>
      ))}
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="audio-device-dropdown-button"
        type="button"
      >
        <span>▼</span>
      </button>
    </Dropdown>
  );
}

export default AudioDeviceDropdown;
