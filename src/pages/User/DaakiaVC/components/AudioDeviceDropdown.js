import React, { useState, useEffect, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { Dropdown } from "antd";
import { useMediaDeviceSelect } from "@livekit/components-react";
import "./AudioDeviceDropdown.scss";

const AudioDeviceDropdown = React.memo(function AudioDeviceDropdown({
  kind = "audioinput",
  onActiveDeviceChange,
  initialSelection,
  className = ""
}) {
  const [selectedDevice, setSelectedDevice] = useState(initialSelection);
  const { devices, activeDeviceId, setActiveMediaDevice } = useMediaDeviceSelect({ kind });

  const displayDevice = useMemo(() => {
    return initialSelection || activeDeviceId;
  }, [initialSelection, activeDeviceId]);


  useEffect(() => {
    setSelectedDevice(displayDevice);
  }, [displayDevice]);

  // Force LiveKit to use the initialSelection device (only when needed)
  useEffect(() => {
    if (initialSelection && initialSelection !== activeDeviceId && devices.length > 0) {
      const deviceExists = devices.some(device => device.deviceId === initialSelection);
      if (deviceExists) {
        setActiveMediaDevice(initialSelection).catch(error => {
          console.error('Failed to set initial device:', error);
        });
      }
    }
  }, [initialSelection, activeDeviceId, setActiveMediaDevice, devices]);

 
  const handleDeviceChange = useCallback(async (deviceId) => {
    try {
      await setActiveMediaDevice(deviceId);
      setSelectedDevice(deviceId);

      onActiveDeviceChange?.(deviceId);
    } catch (error) {
      console.error('Failed to switch device:', error);
    }
  }, [setActiveMediaDevice, onActiveDeviceChange]);

  const dropdownContent = useMemo(() => (
    <div className="audio-device-dropdown-menu">
      {devices.map((device) => {
        const isSelected = selectedDevice === device.deviceId;
        const deviceLabel = device.label || `${kind} ${device.deviceId.slice(0, 8)}...`;

        return (
          <button
            key={device.deviceId}
            className={`device-option ${isSelected ? 'selected' : ''}`}
            onClick={() => handleDeviceChange(device.deviceId)}
            aria-label={`Select ${deviceLabel}`}
            type="button"
          >
            {deviceLabel}
          </button>
        );
      })}
    </div>
  ), [devices, selectedDevice, kind, handleDeviceChange]);

  // Get current device name for accessibility
  const currentDeviceName = useMemo(() => {
    const currentDevice = devices.find(device => device.deviceId === selectedDevice);
    return currentDevice?.label || 'Default Device';
  }, [devices, selectedDevice]);

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
      disabled={devices.length === 0}
    >
      <button
        className="audio-device-dropdown-button"
        type="button"
        aria-label={`Current audio device: ${currentDeviceName}. Click to change device.`}
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <span>▼</span>
      </button>
    </Dropdown>
  );
});


AudioDeviceDropdown.propTypes = {
  kind: PropTypes.oneOf(['audioinput', 'audiooutput', 'videoinput']),
  onActiveDeviceChange: PropTypes.func,
  initialSelection: PropTypes.string,
  className: PropTypes.string,
};


AudioDeviceDropdown.defaultProps = {
  kind: "audioinput",
  onActiveDeviceChange: undefined,
  initialSelection: undefined,
  className: "",
};

export default AudioDeviceDropdown;
