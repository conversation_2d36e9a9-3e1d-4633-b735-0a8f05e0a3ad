import React, { useState, useEffect, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { Dropdown } from "antd";
import { useMediaDeviceSelect } from "@livekit/components-react";
import "./SpeakerDeviceDropdown.scss";

const SpeakerDeviceDropdown = React.memo(function SpeakerDeviceDropdown({
  kind = "audiooutput",
  onActiveDeviceChange,
  initialSelection,
  className = ""
}) {
  const [selectedDevice, setSelectedDevice] = useState(initialSelection);
  const { devices, activeDeviceId, setActiveMediaDevice } = useMediaDeviceSelect({ kind });

  useEffect(() => {
    setSelectedDevice(initialSelection || activeDeviceId);
  }, [initialSelection, activeDeviceId]);

  // Force LiveKit to actually use the initialSelection device
  useEffect(() => {
    if (initialSelection && initialSelection !== activeDeviceId) {
      setActiveMediaDevice(initialSelection).catch(console.error);
    }
  }, [initialSelection, activeDeviceId, setActiveMediaDevice]);

  const handleDeviceChange = useCallback(async (deviceId) => {
    try {
      await setActiveMediaDevice(deviceId);
      setSelectedDevice(deviceId);

      onActiveDeviceChange?.(deviceId);
    } catch (error) {
      console.error('Failed to switch speaker device:', error);
    }
  }, [setActiveMediaDevice, onActiveDeviceChange]);

  const dropdownContent = useMemo(() => (
    <div className="speaker-device-dropdown-menu">
      {devices.map((device) => {
        const isSelected = selectedDevice === device.deviceId;
        const deviceLabel = device.label || `${kind} ${device.deviceId.slice(0, 8)}...`;
        
        return (
          <button
            key={device.deviceId}
            className={`device-option ${isSelected ? 'selected' : ''}`}
            onClick={() => handleDeviceChange(device.deviceId)}
            aria-label={`Select ${deviceLabel}`}
            type="button"
          >
            {deviceLabel}
          </button>
        );
      })}
    </div>
  ), [devices, selectedDevice, kind, handleDeviceChange]);

  const currentDeviceName = useMemo(() => {
    const currentDevice = devices.find(device => device.deviceId === selectedDevice);
    return currentDevice?.label || 'Default Speaker';
  }, [devices, selectedDevice]);

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
      disabled={devices.length === 0}
    >
      <button
        className="speaker-device-dropdown-button"
        type="button"
        aria-label={`Current speaker device: ${currentDeviceName}. Click to change device.`}
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <span>▼</span>
      </button>
    </Dropdown>
  );
});

SpeakerDeviceDropdown.propTypes = {
  kind: PropTypes.oneOf(['audioinput', 'audiooutput', 'videoinput']),
  onActiveDeviceChange: PropTypes.func,
  initialSelection: PropTypes.string,
  className: PropTypes.string,
};

SpeakerDeviceDropdown.defaultProps = {
  kind: "audiooutput",
  onActiveDeviceChange: undefined,
  initialSelection: undefined,
  className: "",
};

export default SpeakerDeviceDropdown;
