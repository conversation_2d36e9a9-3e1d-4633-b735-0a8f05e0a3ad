import React, { useState, useEffect, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { Dropdown } from "antd";
import { useMediaDeviceSelect } from "@livekit/components-react";
import "./AudioSettingsDropdown.scss";

const AudioSettingsDropdown = React.memo(function AudioSettingsDropdown({
  micSelection,
  speakerSelection,
  onMicChange,
  onSpeakerChange,
  className = ""
}) {
  // Get microphone devices
  const { 
    devices: micDevices, 
    activeDeviceId: activeMicId, 
    setActiveMediaDevice: setActiveMicDevice 
  } = useMediaDeviceSelect({ kind: "audioinput" });

  // Get speaker devices
  const { 
    devices: speakerDevices, 
    activeDeviceId: activeSpeakerId, 
    setActiveMediaDevice: setActiveSpeakerDevice 
  } = useMediaDeviceSelect({ kind: "audiooutput" });

  const [selectedMic, setSelectedMic] = useState(micSelection);
  const [selectedSpeaker, setSelectedSpeaker] = useState(speakerSelection);

  // Update selected devices when props change
  useEffect(() => {
    setSelectedMic(micSelection || activeMicId);
  }, [micSelection, activeMicId]);

  useEffect(() => {
    setSelectedSpeaker(speakerSelection || activeSpeakerId);
  }, [speakerSelection, activeSpeakerId]);

  // Force LiveKit to use the initial selections
  useEffect(() => {
    if (micSelection && micSelection !== activeMicId && micDevices.length > 0) {
      const deviceExists = micDevices.some(device => device.deviceId === micSelection);
      if (deviceExists) {
        setActiveMicDevice(micSelection).catch(error => {
          console.error('Failed to set initial mic device:', error);
        });
      }
    }
  }, [micSelection, activeMicId, setActiveMicDevice, micDevices]);

  useEffect(() => {
    if (speakerSelection && speakerSelection !== activeSpeakerId && speakerDevices.length > 0) {
      const deviceExists = speakerDevices.some(device => device.deviceId === speakerSelection);
      if (deviceExists) {
        setActiveSpeakerDevice(speakerSelection).catch(error => {
          console.error('Failed to set initial speaker device:', error);
        });
      }
    }
  }, [speakerSelection, activeSpeakerId, setActiveSpeakerDevice, speakerDevices]);

  // Handle microphone device change
  const handleMicChange = useCallback(async (deviceId) => {
    try {
      await setActiveMicDevice(deviceId);
      setSelectedMic(deviceId);
      onMicChange?.(deviceId);
    } catch (error) {
      console.error('Failed to switch mic device:', error);
    }
  }, [setActiveMicDevice, onMicChange]);

  // Handle speaker device change
  const handleSpeakerChange = useCallback(async (deviceId) => {
    try {
      await setActiveSpeakerDevice(deviceId);
      setSelectedSpeaker(deviceId);
      onSpeakerChange?.(deviceId);
    } catch (error) {
      console.error('Failed to switch speaker device:', error);
    }
  }, [setActiveSpeakerDevice, onSpeakerChange]);

  // Render device options for dropdown
  const renderDeviceOptions = useCallback((devices, selectedDevice, onDeviceChange, deviceType) => {
    return devices.map((device) => {
      const isSelected = selectedDevice === device.deviceId;
      const deviceLabel = device.label || `${deviceType} ${device.deviceId.slice(0, 8)}...`;
      
      return (
        <button
          key={device.deviceId}
          className={`device-option ${isSelected ? 'selected' : ''}`}
          onClick={() => onDeviceChange(device.deviceId)}
          aria-label={`Select ${deviceLabel}`}
          type="button"
        >
          {deviceLabel}
        </button>
      );
    });
  }, []);

  // Memoize dropdown content
  const dropdownContent = useMemo(() => (
    <div className="audio-settings-dropdown-menu">
      <div className="audio-settings-row">
        {/* Microphone Dropdown */}
        <div className="audio-setting-section">
          <label className="audio-setting-label">🎤 Microphone</label>
          <div className="device-dropdown-container">
            <Dropdown
              menu={{
                items: micDevices.map(device => ({
                  key: device.deviceId,
                  label: (
                    <div
                      className={`device-option ${selectedMic === device.deviceId ? 'selected' : ''}`}
                      onClick={() => handleMicChange(device.deviceId)}
                    >
                      {device.label || `Microphone ${device.deviceId.slice(0, 8)}...`}
                    </div>
                  )
                }))
              }}
              trigger={['click']}
              placement="bottomLeft"
            >
              <button className="device-select-button" type="button">
                <span className="device-name">
                  {micDevices.find(d => d.deviceId === selectedMic)?.label || 'Default Mic'}
                </span>
                <span className="dropdown-arrow">▼</span>
              </button>
            </Dropdown>
          </div>
        </div>

        {/* Speaker Dropdown */}
        <div className="audio-setting-section">
          <label className="audio-setting-label">🔊 Speaker</label>
          <div className="device-dropdown-container">
            <Dropdown
              menu={{
                items: speakerDevices.map(device => ({
                  key: device.deviceId,
                  label: (
                    <div
                      className={`device-option ${selectedSpeaker === device.deviceId ? 'selected' : ''}`}
                      onClick={() => handleSpeakerChange(device.deviceId)}
                    >
                      {device.label || `Speaker ${device.deviceId.slice(0, 8)}...`}
                    </div>
                  )
                }))
              }}
              trigger={['click']}
              placement="bottomLeft"
            >
              <button className="device-select-button" type="button">
                <span className="device-name">
                  {speakerDevices.find(d => d.deviceId === selectedSpeaker)?.label || 'Default Speaker'}
                </span>
                <span className="dropdown-arrow">▼</span>
              </button>
            </Dropdown>
          </div>
        </div>
      </div>
    </div>
  ), [micDevices, speakerDevices, selectedMic, selectedSpeaker, handleMicChange, handleSpeakerChange]);

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
      disabled={micDevices.length === 0 && speakerDevices.length === 0}
    >
      <button
        className="audio-settings-dropdown-button"
        type="button"
        aria-label="Audio settings - microphone and speaker selection"
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <span>▼</span>
      </button>
    </Dropdown>
  );
});

AudioSettingsDropdown.propTypes = {
  micSelection: PropTypes.string,
  speakerSelection: PropTypes.string,
  onMicChange: PropTypes.func,
  onSpeakerChange: PropTypes.func,
  className: PropTypes.string,
};

AudioSettingsDropdown.defaultProps = {
  micSelection: undefined,
  speakerSelection: undefined,
  onMicChange: undefined,
  onSpeakerChange: undefined,
  className: "",
};

export default AudioSettingsDropdown;
