import React from "react";
import PropTypes from "prop-types";
import { Dropdown } from "antd";
import AudioDeviceDropdown from "./AudioDeviceDropdown";
import SpeakerDeviceDropdown from "./SpeakerDeviceDropdown";
import "./CombinedAudioDropdown.scss";

const CombinedAudioDropdown = React.memo(function CombinedAudioDropdown({
  micSelection,
  speakerSelection,
  onMicChange,
  onSpeakerChange,
  className = ""
}) {
  const dropdownContent = (
    <div className="combined-audio-dropdown-menu">
      <div className="audio-settings-row">
        {/* Microphone Section */}
        <div className="audio-setting-section">
          <label className="audio-setting-label">🎤 Microphone</label>
          <div className="device-dropdown-wrapper">
            <AudioDeviceDropdown
              kind="audioinput"
              onActiveDeviceChange={onMicChange}
              initialSelection={micSelection}
              className="device-dropdown"
            />
          </div>
        </div>

        {/* Speaker Section */}
        <div className="audio-setting-section">
          <label className="audio-setting-label">🔊 Speaker</label>
          <div className="device-dropdown-wrapper">
            <SpeakerDeviceDropdown
              kind="audiooutput"
              onActiveDeviceChange={onSpeakerChange}
              initialSelection={speakerSelection}
              className="device-dropdown"
            />
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="combined-audio-dropdown-button"
        type="button"
        aria-label="Audio settings - microphone and speaker selection"
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <span>▼</span>
      </button>
    </Dropdown>
  );
});

CombinedAudioDropdown.propTypes = {
  micSelection: PropTypes.string,
  speakerSelection: PropTypes.string,
  onMicChange: PropTypes.func,
  onSpeakerChange: PropTypes.func,
  className: PropTypes.string,
};

CombinedAudioDropdown.defaultProps = {
  micSelection: undefined,
  speakerSelection: undefined,
  onMicChange: undefined,
  onSpeakerChange: undefined,
  className: "",
};

export default CombinedAudioDropdown;
