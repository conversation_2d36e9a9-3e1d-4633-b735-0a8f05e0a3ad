.speaker-device-dropdown-button {
  background: transparent;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:focus {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.speaker-device-dropdown-menu {
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 200px;
  max-width: 300px;
  border: 1px solid #d9d9d9;

  .device-option {
    width: 100%;
    padding: 8px 12px;
    border: none;
    background: transparent;
    text-align: left;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      background-color: #f5f5f5;
    }

    &.selected {
      background-color: #e6f7ff;
      color: #1890ff;
      font-weight: 500;
    }

    &:focus {
      outline: 2px solid #1890ff;
      outline-offset: -2px;
    }
  }
}

// Dark theme support
[data-lk-theme="dark"] {
  .speaker-device-dropdown-menu {
    background: #1f1f1f;
    border-color: #434343;

    .device-option {
      color: white;

      &:hover {
        background-color: #2f2f2f;
      }

      &.selected {
        background-color: #111b26;
        color: #40a9ff;
      }
    }
  }
}
