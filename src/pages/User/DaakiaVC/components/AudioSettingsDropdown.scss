.audio-settings-dropdown-button {
  background: transparent;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:focus {
    outline: 2px solid #1890ff;
    outline-offset: 2px;
  }
}

.audio-settings-dropdown-menu {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 400px;
  border: 1px solid #d9d9d9;
}

.audio-settings-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.audio-setting-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.audio-setting-label {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.device-dropdown-container {
  position: relative;
}

.device-select-button {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.device-name {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.dropdown-arrow {
  color: #8c8c8c;
  font-size: 12px;
  margin-left: 8px;
}

.device-option {
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    background-color: #f5f5f5;
  }

  &.selected {
    background-color: #e6f7ff;
    color: #1890ff;
    font-weight: 500;
  }

  &:focus {
    outline: 2px solid #1890ff;
    outline-offset: -2px;
  }
}

// Responsive design for smaller screens
@media (max-width: 768px) {
  .audio-settings-dropdown-menu {
    min-width: 300px;
    padding: 12px;
  }

  .audio-settings-row {
    flex-direction: column;
    gap: 16px;
  }

  .device-name {
    max-width: 120px;
  }
}

// Dark theme support (if needed)
[data-lk-theme="dark"] {
  .audio-settings-dropdown-menu {
    background: #1f1f1f;
    border-color: #434343;
    color: white;
  }

  .audio-setting-label {
    color: #ffffff;
  }

  .device-select-button {
    background: #2f2f2f;
    border-color: #434343;
    color: white;

    &:hover {
      border-color: #40a9ff;
    }
  }

  .device-option {
    color: white;

    &:hover {
      background-color: #2f2f2f;
    }

    &.selected {
      background-color: #111b26;
      color: #40a9ff;
    }
  }

  .dropdown-arrow {
    color: #8c8c8c;
  }
}
